"""
模型选择器实现
负责根据文件类型和用户偏好选择合适的AI模型
"""
from typing import List, Dict, Any, Optional
from ..interfaces import IModelSelector, IModelClient
from .model_client_wrapper import ModelClientWrapper
from utils.llms import model_client, deepseek_model_client, moonshot_model_client
from utils.logging_config import get_logger

logger = get_logger(__name__)


class ModelSelector(IModelSelector):
    """模型选择器实现"""
    
    def __init__(self):
        self._models: Dict[str, IModelClient] = {}
        self._initialize_models()
    
    def _initialize_models(self):
        """初始化可用模型"""
        # 包装Qwen模型客户端
        if model_client:
            self._models["qwen"] = ModelClientWrapper(
                client=model_client,
                model_name="Qwen",
                supports_vision=True
            )
            logger.info("Qwen模型客户端已注册")
        
        # 包装DeepSeek模型客户端
        if deepseek_model_client:
            self._models["deepseek"] = ModelClientWrapper(
                client=deepseek_model_client,
                model_name="DeepSeek",
                supports_vision=False
            )
            logger.info("DeepSeek模型客户端已注册")
        
        # 包装Moonshot模型客户端
        if moonshot_model_client:
            self._models["moonshot"] = ModelClientWrapper(
                client=moonshot_model_client,
                model_name="Moonshot",
                supports_vision=False
            )
            logger.info("Moonshot模型客户端已注册")
        
        logger.info(f"模型选择器初始化完成，可用模型: {list(self._models.keys())}")
    
    def select_model_for_file(self, file_path: str, preferred_model: Optional[str] = None) -> IModelClient:
        """根据文件类型和用户偏好选择合适的模型"""
        file_extension = file_path.lower().split('.')[-1] if '.' in file_path else ''
        
        # 如果用户指定了偏好模型，优先使用
        if preferred_model and preferred_model.lower() in self._models:
            selected_model = self._models[preferred_model.lower()]
            logger.info(f"使用用户偏好模型: {selected_model.get_model_name()}")
            return selected_model
        
        # 图像文件使用支持视觉的模型
        if file_extension in ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp']:
            for model_key in ['qwen']:  # 优先使用Qwen处理图像
                if model_key in self._models and self._models[model_key].supports_vision():
                    selected_model = self._models[model_key]
                    logger.info(f"为图像文件选择模型: {selected_model.get_model_name()}")
                    return selected_model
        
        # 非图像文件的模型选择策略
        # 优先使用DeepSeek（如果可用），然后是Moonshot，最后是Qwen
        for model_key in ['deepseek', 'moonshot', 'qwen']:
            if model_key in self._models:
                selected_model = self._models[model_key]
                logger.info(f"为文本文件选择模型: {selected_model.get_model_name()}")
                return selected_model
        
        raise ValueError("没有可用的模型客户端")
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用模型列表"""
        models = []
        
        for model_key, model_client in self._models.items():
            model_info = {
                "id": model_key,
                "name": model_client.get_model_name(),
                "supports_vision": model_client.supports_vision(),
                "supports_streaming": model_client.supports_streaming()
            }
            
            # 添加模型描述和推荐用途
            if model_key == "qwen":
                model_info.update({
                    "description": "通义千问大模型，支持视觉理解",
                    "recommended_for": ["图像分析", "多模态任务"]
                })
            elif model_key == "deepseek":
                model_info.update({
                    "description": "DeepSeek大模型，专注于代码和逻辑推理",
                    "recommended_for": ["文本处理", "代码分析", "逻辑推理"]
                })
            elif model_key == "moonshot":
                model_info.update({
                    "description": "月之暗面大模型，平衡性能与效果",
                    "recommended_for": ["通用文本处理", "测试用例生成"]
                })
            
            models.append(model_info)
        
        return models
    
    def get_model_by_key(self, model_key: str) -> Optional[IModelClient]:
        """根据键获取模型客户端"""
        return self._models.get(model_key.lower())
    
    def has_vision_capable_model(self) -> bool:
        """检查是否有支持视觉的模型"""
        return any(model.supports_vision() for model in self._models.values())
