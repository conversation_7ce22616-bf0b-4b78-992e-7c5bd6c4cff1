from fastapi import APIRouter, UploadFile, File, Form, Request
from fastapi.responses import StreamingResponse, FileResponse
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel
import os
import json
import uuid
from datetime import datetime
import asyncio

from models.test_case import TestCase, TestCaseRequest, TestCaseResponse
from services.service_registry import (
    get_model_selector, get_test_case_generator, get_export_service
)
from utils.file_utils import file_manager
from utils.exceptions import (
    ValidationError, FileError, ResourceNotFoundError,
    AIServiceError, handle_unexpected_error
)
from utils.logging_config import get_logger, security_logger, ai_service_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/api/test-cases",
    tags=["test-cases"],
    responses={404: {"description": "Not found"}},
)


@router.get("/models")
async def get_available_models():
    """
    获取可用的AI模型列表

    返回:
        包含可用模型信息的字典
    """
    try:
        model_selector = get_model_selector()
        models = model_selector.get_available_models()

        # 确定默认策略
        default_strategy = {
            "image_files": "qwen",
            "text_files": "deepseek"
        }

        # 如果deepseek不可用，使用moonshot，最后使用qwen
        available_model_ids = [model["id"] for model in models]
        if "deepseek" not in available_model_ids:
            default_strategy["text_files"] = "moonshot" if "moonshot" in available_model_ids else "qwen"

        return {
            "available_models": models,
            "default_strategy": default_strategy
        }
    except Exception as e:
        logger.error(f"获取可用模型失败: {str(e)}")
        raise handle_unexpected_error(e, "get_available_models")


@router.post("/generate")
async def generate_test_cases(
    request: Request,
    file: UploadFile = File(...),
    context: str = Form(...),
    requirements: str = Form(...),
    preferred_model: str = Form(default="")
):
    """
    从上传的文件、上下文和需求生成测试用例

    参数:
        request: FastAPI请求对象
        file: 上传的文件（图像、PDF或OpenAPI文档）
        context: 测试用例生成的上下文信息
        requirements: 测试用例生成的需求
        preferred_model: 用户偏好的模型 ("qwen", "deepseek", "moonshot")

    返回:
        包含生成的测试用例的流式响应
    """
    try:
        # 验证输入参数
        if not context or not context.strip():
            raise ValidationError("上下文信息不能为空", "context")

        if not requirements or not requirements.strip():
            raise ValidationError("需求信息不能为空", "requirements")

        if len(context.strip()) < 5:
            raise ValidationError("上下文信息至少需要5个字符", "context")

        if len(requirements.strip()) < 5:
            raise ValidationError("需求信息至少需要5个字符", "requirements")

        # 获取客户端IP
        client_ip = request.client.host if request.client else "unknown"

        # 使用新的文件管理器保存文件
        file_path, file_info = await file_manager.save_uploaded_file(file, client_ip)

        logger.info(f"开始生成测试用例 - 文件: {file_info['original_filename']}, "
                   f"大小: {file_info['size']} bytes, 类型: {file_info['extension']}")

        # 记录AI服务开始
        selected_model = preferred_model.strip() if preferred_model else "auto-selected"
        ai_service_logger.log_generation_start(
            file_type=file_info['extension'],
            model=selected_model,
            context_length=len(context) + len(requirements)
        )

        # 生成测试用例
        test_case_generator = get_test_case_generator()
        return StreamingResponse(
            test_case_generator.generate_test_cases_stream(
                file_path,
                context.strip(),
                requirements.strip(),
                preferred_model.strip() if preferred_model else None
            ),
            media_type="text/markdown"
        )

    except (ValidationError, FileError) as e:
        logger.warning(f"请求验证失败: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"生成测试用例失败: {str(e)}")
        raise handle_unexpected_error(e, "generate_test_cases")

class MindMapRequest(BaseModel):
    test_cases: List[Dict[str, Any]]

@router.post("/generate-mindmap")
async def generate_mindmap_from_test_cases(
    request: MindMapRequest
):
    """
    从测试用例生成思维导图数据

    参数:
        request: 包含测试用例列表的请求体

    返回:
        思维导图的JSON数据
    """
    try:
        if not request.test_cases:
            raise ValidationError("测试用例列表不能为空")

        logger.info(f"开始生成思维导图 - 测试用例数量: {len(request.test_cases)}")

        test_case_generator = get_test_case_generator()
        mindmap_data = test_case_generator.generate_mindmap_from_test_cases(request.test_cases)

        logger.info("思维导图生成成功")
        return {"mindmap": mindmap_data}

    except ValidationError as e:
        logger.warning(f"思维导图生成请求验证失败: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"生成思维导图失败: {str(e)}")
        raise AIServiceError("生成思维导图失败", str(e))

@router.post("/export")
async def export_test_cases(test_cases: List[Union[TestCase, Dict[str, Any]]]):
    """
    将测试用例导出到Excel

    参数:
        test_cases: 要导出的测试用例列表

    返回:
        下载生成的Excel文件的URL
    """
    try:
        if not test_cases:
            raise ValidationError("测试用例列表不能为空")

        logger.info(f"开始导出测试用例到Excel - 数量: {len(test_cases)}")

        # 生成Excel文件
        export_service = get_export_service()
        excel_path = export_service.export_to_excel(test_cases)

        logger.info(f"Excel文件生成成功: {excel_path}")

        # 返回文件供下载
        return FileResponse(
            path=excel_path,
            filename=os.path.basename(excel_path),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )

    except ValidationError as e:
        logger.warning(f"导出请求验证失败: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"导出测试用例失败: {str(e)}")
        raise handle_unexpected_error(e, "export_test_cases")


@router.post("/export-xmind")
async def export_test_cases_to_xmind(test_cases: List[Union[TestCase, Dict[str, Any]]]):
    """
    将测试用例导出到XMind

    参数:
        test_cases: 要导出的测试用例列表

    返回:
        下载生成的XMind文件的URL
    """
    try:
        if not test_cases:
            raise ValidationError("测试用例列表不能为空")

        logger.info(f"开始导出测试用例到XMind - 数量: {len(test_cases)}")

        # 生成XMind文件
        export_service = get_export_service()
        xmind_path = export_service.export_to_xmind(test_cases)

        logger.info(f"XMind文件生成成功: {xmind_path}")

        # 返回文件供下载
        return FileResponse(
            path=xmind_path,
            filename=os.path.basename(xmind_path),
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename={os.path.basename(xmind_path)}"}
        )

    except ValidationError as e:
        logger.warning(f"XMind导出请求验证失败: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"导出测试用例到XMind失败: {str(e)}")
        raise handle_unexpected_error(e, "export_test_cases_to_xmind")


@router.get("/download/{filename}")
async def download_file(filename: str):
    """
    下载生成的文件（Excel或XMind）

    参数:
        filename: 要下载的文件名

    返回:
        供下载的文件
    """
    try:
        # 验证文件名和扩展名
        if not filename:
            raise ValidationError("文件名不能为空")

        # 检查支持的文件类型
        if not (filename.endswith('.xlsx') or filename.endswith('.xmind')):
            raise ValidationError("不支持的文件类型，仅支持 .xlsx 和 .xmind 文件")

        # 构建安全的文件路径
        file_path = os.path.join("results", filename)

        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise ResourceNotFoundError("文件", filename)

        # 获取文件信息用于日志
        file_info = file_manager.get_file_info(file_path)
        if file_info:
            logger.info(f"下载文件: {filename} ({file_info['size']} bytes)")

        # 根据文件类型设置不同的MIME类型
        if filename.endswith('.xlsx'):
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        elif filename.endswith('.xmind'):
            media_type = "application/zip"

        return FileResponse(
            path=file_path,
            filename=filename,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except (ValidationError, ResourceNotFoundError) as e:
        logger.warning(f"文件下载请求失败: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"文件下载失败: {str(e)}")
        raise handle_unexpected_error(e, "download_file")
