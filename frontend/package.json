{"name": "test-case-generator-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.14", "@mui/material": "^5.15.14", "@types/d3": "^7.4.3", "axios": "^1.6.8", "markmap-lib": "^0.18.12", "markmap-view": "^0.18.12", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-markdown": "^9.0.1", "react-markmap": "^0.0.1", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.5.0", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "remark-gfm": "^4.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "devDependencies": {"@babel/plugin-transform-private-property-in-object": "^7.24.1", "@types/react-window": "^1.8.8"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}